package com.kengenesius.dreamshops.service.shoporder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import com.kengenesius.dreamshops.dto.ShopOrderDto;
import com.kengenesius.dreamshops.enums.OrderStatus;
import com.kengenesius.dreamshops.exception.ResourceNotFoundException;
import com.kengenesius.dreamshops.model.Cart;
import com.kengenesius.dreamshops.model.Product;
import com.kengenesius.dreamshops.model.ShopOrder;
import com.kengenesius.dreamshops.model.ShopOrderItem;
import com.kengenesius.dreamshops.repository.ProductRepository;
import com.kengenesius.dreamshops.repository.ShopOrderRepository;
import com.kengenesius.dreamshops.service.cart.ICartService;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ShopOrderService implements IShopOrderService{

    private final ShopOrderRepository shopOrderRepository;
    private final ProductRepository productRepository;
    private final ICartService cartService;
    private final ModelMapper modelMapper;

    @Override
    public ShopOrderDto placeOrder(UUID userId) {
        Cart cart = cartService.getCartByUserId(userId);

        ShopOrder order = createOrder(cart);
        List<ShopOrderItem> orderItems = createOrderItems(order, cart);

        order.setOrderItems(new HashSet<>(orderItems));
        order.setTotalAmount(calculateTotalAmount(orderItems));
        ShopOrder savedOrder = shopOrderRepository.save(order);
        
        cartService.clearCart(cart.getId());
        return convertToShopOrderDto(savedOrder);
    }

    private ShopOrder createOrder(Cart cart)
    {
        ShopOrder order = new ShopOrder();
        order.setUser(cart.getUser());
        order.setOrderStatus(OrderStatus.PENDING);
        order.setOrderDate(LocalDate.now());
        return order;
    }

    private List<ShopOrderItem> createOrderItems(ShopOrder order, Cart cart){
        return cart.getCartItems().stream()
            .map(cartItem -> {
                Product product = cartItem.getProduct();
                product.setInventory(product.getInventory() - cartItem.getQuantity());
                productRepository.save(product);
                return new ShopOrderItem(order, product, cartItem.getQuantity(), cartItem.getUnitPrice());
            }).toList();
        
    }

    private BigDecimal calculateTotalAmount(List<ShopOrderItem> orderItemList) {
        return orderItemList.stream()
            .map(item -> item.getPrice().multiply(new BigDecimal(item.getQuantity())))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public ShopOrderDto getOrder(UUID orderId) {
        return shopOrderRepository.findById(orderId)
            .map(this::convertToShopOrderDto)
            .orElseThrow(() -> new ResourceNotFoundException("Order not found!"));
    }

    @Override
    public List<ShopOrderDto> getUserOrders(UUID userId) {
        return shopOrderRepository.findByUserId(userId).stream().map(this::convertToShopOrderDto).toList();
    }

    @Override
    public ShopOrderDto convertToShopOrderDto(ShopOrder order) {
        return modelMapper.map(order, ShopOrderDto.class);
    }
}
