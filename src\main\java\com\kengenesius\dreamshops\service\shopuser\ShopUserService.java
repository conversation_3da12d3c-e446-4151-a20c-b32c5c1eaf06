package com.kengenesius.dreamshops.service.shopuser;

import java.util.Optional;
import java.util.UUID;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import com.kengenesius.dreamshops.dto.ShopUserDto;
import com.kengenesius.dreamshops.exception.AlreadyExistsException;
import com.kengenesius.dreamshops.exception.ResourceNotFoundException;
import com.kengenesius.dreamshops.model.ShopUser;
import com.kengenesius.dreamshops.repository.ShopUserRepository;
import com.kengenesius.dreamshops.request.CreateUserRequest;
import com.kengenesius.dreamshops.request.UpdateUserRequest;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ShopUserService implements IShopUserService{
    
    private final ShopUserRepository userRepository;
    private final ModelMapper modelMapper;

    @Override
    public ShopUser getUserById(UUID userId) {
        return userRepository.findById(userId).orElseThrow(() -> new ResourceNotFoundException("User not found!"));
    }

    @Override
    public ShopUser createUser(CreateUserRequest request) {
        return Optional.of(request)
            .filter(user -> !userRepository.existsByEmail(request.getEmail()))
            .map(req -> {
                ShopUser user = new ShopUser();
                user.setFirstName(request.getFirstName());
                user.setLastName(request.getLastName());
                user.setEmail(request.getEmail());
                user.setPassword(request.getPassword());
                return userRepository.save(user);
            }).orElseThrow(() -> new AlreadyExistsException(request.getEmail() + " already exists!"));
    }

    @Override
    public ShopUser updateUser(UpdateUserRequest request, UUID userId) {
        return userRepository.findById(userId).map(existingUser ->{
            existingUser.setFirstName(request.getFirstName());
            existingUser.setLastName(request.getLastName());
            return userRepository.save(existingUser);
        }).orElseThrow(() -> new ResourceNotFoundException("User not found!"));
    }

    @Override
    public void deleteUser(UUID userId) {
        userRepository.findById(userId)
            .ifPresentOrElse(userRepository::delete, () -> {
                throw new ResourceNotFoundException("User not found!");
            });
    }

    @Override
    public ShopUserDto convertToShopUserDto(ShopUser user){
        return modelMapper.map(user, ShopUserDto.class);
    }
}
