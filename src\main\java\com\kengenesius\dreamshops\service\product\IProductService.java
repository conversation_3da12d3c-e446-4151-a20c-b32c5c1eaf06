package com.kengenesius.dreamshops.service.product;

import java.util.List;
import java.util.UUID;

import com.kengenesius.dreamshops.dto.ProductDto;
import com.kengenesius.dreamshops.model.Product;
import com.kengenesius.dreamshops.request.ProductAddRequest;
import com.kengenesius.dreamshops.request.ProductUpdateRequest;

public interface IProductService {

    Product addProduct(ProductAddRequest request);
    Product getProductById(UUID id);
    Product updateProduct(ProductUpdateRequest request, UUID id);
    void deleteProductById(UUID id);
    
    List<Product> getAllProducts();
    List<Product> getProductsByCategory(String category);
    List<Product> getProductsByBrand(String brand);
    List<Product> getProductsByCategoryAndBrand(String category, String brand);
    List<Product> getProductsByName(String name);
    List<Product> getProductsByBrandAndName(String brand, String name);

    Long countProductsByBrandAndName(String brand, String name);
    ProductDto convertToProductDto(Product product);
    List<ProductDto> convertToProductDtoList(List<Product> products);

}
