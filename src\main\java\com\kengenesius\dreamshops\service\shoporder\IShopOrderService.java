package com.kengenesius.dreamshops.service.shoporder;

import java.util.List;
import java.util.UUID;

import com.kengenesius.dreamshops.dto.ShopOrderDto;
import com.kengenesius.dreamshops.model.ShopOrder;

public interface IShopOrderService {
    ShopOrderDto placeOrder(UUID userId);
    ShopOrderDto getOrder(UUID orderId);
    List<ShopOrderDto> getUserOrders(UUID userId);
    ShopOrderDto convertToShopOrderDto(ShopOrder order);
}
