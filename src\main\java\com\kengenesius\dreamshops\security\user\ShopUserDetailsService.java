package com.kengenesius.dreamshops.security.user;

import java.util.Optional;

import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.kengenesius.dreamshops.model.ShopUser;
import com.kengenesius.dreamshops.repository.ShopUserRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ShopUserDetailsService implements UserDetailsService {

    private final ShopUserRepository userRepository;

    @Override
    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {
        ShopUser user = Optional.ofNullable(userRepository.findByEmail(email))
        .orElseThrow(() -> new UsernameNotFoundException(email + " not found!"));
        return ShopUserDetails.buildUserDetails(user);
    }

}
