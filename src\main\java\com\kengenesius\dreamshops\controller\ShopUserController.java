package com.kengenesius.dreamshops.controller;

import static org.springframework.http.HttpStatus.CONFLICT;
import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;
import static org.springframework.http.HttpStatus.NOT_FOUND;

import java.util.UUID;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kengenesius.dreamshops.dto.ShopUserDto;
import com.kengenesius.dreamshops.exception.AlreadyExistsException;
import com.kengenesius.dreamshops.exception.ResourceNotFoundException;
import com.kengenesius.dreamshops.model.ShopUser;
import com.kengenesius.dreamshops.request.CreateUserRequest;
import com.kengenesius.dreamshops.request.UpdateUserRequest;
import com.kengenesius.dreamshops.response.ApiResponse;
import com.kengenesius.dreamshops.service.shopuser.IShopUserService;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("${api.prefix}/users")
@RequiredArgsConstructor
public class ShopUserController {
    private final IShopUserService userService;

    @GetMapping("/user/{userId}")
    public ResponseEntity<ApiResponse> getUserById(@PathVariable UUID userId)
    {
        try{
            ShopUser user = userService.getUserById(userId);
            ShopUserDto userDto = userService.convertToShopUserDto(user);
            return ResponseEntity.ok(new ApiResponse("User found!", userDto));
        } catch(ResourceNotFoundException e)
        {
            return ResponseEntity.status(NOT_FOUND).body(new ApiResponse(e.getMessage(), null));
        }
    }

    @PostMapping("/add")
    public ResponseEntity<ApiResponse> createUser(@RequestBody CreateUserRequest request){
        try{
            ShopUser user = userService.createUser(request);
            ShopUserDto userDto = userService.convertToShopUserDto(user);
            return ResponseEntity.ok(new ApiResponse("User created!", userDto));
        } catch(AlreadyExistsException e)
        {
            return ResponseEntity.status(CONFLICT).body(new ApiResponse(e.getMessage(), null));
        }
    }

    @PutMapping("/user/{userId}/update")
    public ResponseEntity<ApiResponse> updateUser(@PathVariable UUID userId, @RequestBody UpdateUserRequest request){
        try{
            ShopUser user = userService.updateUser(request, userId);
            ShopUserDto userDto = userService.convertToShopUserDto(user);
            return ResponseEntity.ok(new ApiResponse("User updated!", userDto));
        }catch(ResourceNotFoundException e)
        {
            return ResponseEntity.status(NOT_FOUND).body(new ApiResponse(e.getMessage(), null));
        }
    }

    @DeleteMapping("/user/{userId}/delete")
    public ResponseEntity<ApiResponse> deleteUser(@PathVariable UUID userId)
    {
        try{
            userService.deleteUser(userId);
            return ResponseEntity.ok(new ApiResponse("User deleted!", null));
        }catch(ResourceNotFoundException e)
        {
            return ResponseEntity.status(NOT_FOUND).body(new ApiResponse(e.getMessage(), null));
        }
    }
}
