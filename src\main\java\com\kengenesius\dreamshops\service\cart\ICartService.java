package com.kengenesius.dreamshops.service.cart;

import java.math.BigDecimal;
import java.util.UUID;

import com.kengenesius.dreamshops.model.Cart;
import com.kengenesius.dreamshops.model.ShopUser;

public interface ICartService {

    Cart getCart(UUID id);
    void clearCart(UUID id);
    BigDecimal getTotalPrice(UUID id);
    Cart initializeCart(ShopUser user);
    Cart getCartByUserId(UUID userId);
}
