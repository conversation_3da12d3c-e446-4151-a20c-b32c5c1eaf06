package com.kengenesius.dreamshops.dto;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import com.kengenesius.dreamshops.model.Category;

import lombok.Data;

@Data
public class ProductDto {
    private UUID id;
    private String name;
    private String brand;
    private BigDecimal price;
    private int inventory;
    private String description;
    private Category category;
    private List<ImageDto> images;
}
