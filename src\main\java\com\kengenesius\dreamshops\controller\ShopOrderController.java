package com.kengenesius.dreamshops.controller;

import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;
import static org.springframework.http.HttpStatus.NOT_FOUND;

import java.util.List;
import java.util.UUID;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.kengenesius.dreamshops.dto.ShopOrderDto;
import com.kengenesius.dreamshops.exception.ResourceNotFoundException;
import com.kengenesius.dreamshops.model.ShopOrder;
import com.kengenesius.dreamshops.response.ApiResponse;
import com.kengenesius.dreamshops.service.shoporder.IShopOrderService;

import lombok.RequiredArgsConstructor;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@RestController
@RequiredArgsConstructor
@RequestMapping("${api.prefix}/orders")
public class ShopOrderController {
    private final IShopOrderService orderService;

    @PostMapping("/order")    
    public ResponseEntity<ApiResponse> createOrder(@RequestParam UUID userId) {
        try{
            ShopOrderDto order = orderService.placeOrder(userId);
            return ResponseEntity.ok(new ApiResponse("Order created!", order));
        } catch(Exception e)
        {
            return ResponseEntity.status(INTERNAL_SERVER_ERROR).body(new ApiResponse(e.getMessage(), null));
        }
    }

    @GetMapping("/order/{orderId}")
    public ResponseEntity<ApiResponse> getOrder(@PathVariable UUID orderId)
    {
        try{
            ShopOrderDto order = orderService.getOrder(orderId);
            return ResponseEntity.ok(new ApiResponse("Order found!", order));
        } catch(ResourceNotFoundException e)
        {
            return ResponseEntity.status(NOT_FOUND).body(new ApiResponse(e.getMessage(), null));
        }
    }

    @GetMapping("/order/by/userId")
    public ResponseEntity<ApiResponse> getUserOrders(@RequestParam UUID userId){
        try{
            List<ShopOrderDto> orders = orderService.getUserOrders(userId);
            return ResponseEntity.ok(new ApiResponse("Orders found!", orders));
        } catch(ResourceNotFoundException e)
        {
            return ResponseEntity.status(NOT_FOUND).body(new ApiResponse(e.getMessage(), null));
        }
    }
}
