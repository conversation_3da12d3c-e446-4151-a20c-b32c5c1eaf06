package com.kengenesius.dreamshops.security.jwt;

import java.security.Key;
import java.util.Date;
import java.util.List;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;

import com.kengenesius.dreamshops.security.user.ShopUserDetails;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;

public class JwtUtils {
    private String jwtSecret;
    private int jwtExpirationTime;

    public String generateTokenForUser(Authentication authentication)
    {
        ShopUserDetails userPrincipal = (ShopUserDetails) authentication.getPrincipal();
        
        List<String> roles = userPrincipal.getAuthorities().stream()
            .map(GrantedAuthority::getAuthority).toList();
        
        return Jwts.builder()
            .subject(userPrincipal.getEmail())
            .claim("id", userPrincipal.getId())
            .claim("roles", roles)
            .issuedAt(new Date())
            .expiration(new Date((new Date()).getTime() + jwtExpirationTime))
            .signWith(key(), Jwts.SIG.HS256)
            .compact();
    }

    private Key key() {
        return Jwts.SIG.HS256.key().build(Decoders.BASE64.decode(jwtSecret));
    }
}
