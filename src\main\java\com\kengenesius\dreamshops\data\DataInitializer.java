package com.kengenesius.dreamshops.data;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import com.kengenesius.dreamshops.model.ShopUser;
import com.kengenesius.dreamshops.repository.ShopUserRepository;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class DataInitializer implements ApplicationListener<ApplicationReadyEvent>{
    private final ShopUserRepository userRepository;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        createDefaultUserIfNotExists();
    }

    private void createDefaultUserIfNotExists(){
        for(int i=1; i <= 5; i++)
        {
            String defaultEmail = "user"+i+"@gmail.com";
            if(userRepository.existsByEmail(defaultEmail))
                continue;
            
            ShopUser user = new ShopUser();
            user.setFirstName("User"+i);
            user.setLastName("Last"+i);
            user.setEmail(defaultEmail);
            user.setPassword("password");
            userRepository.save(user);
            System.out.println("Default user "+i+" created!");
        }
    }
}
