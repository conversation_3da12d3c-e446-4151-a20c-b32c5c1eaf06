package com.kengenesius.dreamshops.service.product;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import com.kengenesius.dreamshops.dto.ImageDto;
import com.kengenesius.dreamshops.dto.ProductDto;
import com.kengenesius.dreamshops.exception.AlreadyExistsException;
import com.kengenesius.dreamshops.exception.ProductNotfoundException;
import com.kengenesius.dreamshops.exception.ResourceNotFoundException;
import com.kengenesius.dreamshops.model.Category;
import com.kengenesius.dreamshops.model.Image;
import com.kengenesius.dreamshops.model.Product;
import com.kengenesius.dreamshops.repository.CategoryRepository;
import com.kengenesius.dreamshops.repository.ImageRepository;
import com.kengenesius.dreamshops.repository.ProductRepository;
import com.kengenesius.dreamshops.request.ProductAddRequest;
import com.kengenesius.dreamshops.request.ProductUpdateRequest;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ProductService implements IProductService {

    private final ProductRepository productRepository;
    private final CategoryRepository categoryRepository;
    private final ImageRepository imageRepository;
    private final ModelMapper modelMapper;

    @Override
    public Product addProduct(ProductAddRequest request) {
        // Check if the category exists
        // If Exists, set it as the new product's category
        // If Not Exists, create a new category and set it as the new product's category

        if(productExists(request.getName(), request.getBrand())){
            throw new AlreadyExistsException(request.getBrand()+ " " + request.getName() +" already exists, you may update this product instead!");
        }

        Category category = Optional.ofNullable(categoryRepository.findByName(request.getCategory().getName()))
            .orElseGet(() -> {
                Category newCategory = new Category(request.getCategory().getName());
                return categoryRepository.save(newCategory);
            });
        request.setCategory(category);
        return productRepository.save(createProduct(request, category));
    }

    private boolean productExists(String name, String brand) {
        return productRepository.existsByNameAndBrand(name, brand);
    }

    private Product createProduct(ProductAddRequest request, Category category) {
        return new Product(
            request.getName(),
            request.getBrand(),
            request.getPrice(),
            request.getInventory(),
            request.getDescription(),
            category
        );
    }

    @Override
    public Product getProductById(UUID id) {
        return productRepository.findById(id)
        .orElseThrow(() ->  new ResourceNotFoundException("Product not found!"));
    }

    @Override
    public Product updateProduct(ProductUpdateRequest request, UUID id) {
        return productRepository.findById(id)
            .map(existingProduct -> updateExistingProduct(existingProduct, request))
            .map(productRepository :: save)
            .orElseThrow(() -> new ResourceNotFoundException("Product not found!"));
    }

    private Product updateExistingProduct(Product existingProduct, ProductUpdateRequest request)
    {
        existingProduct.setName(request.getName());
        existingProduct.setBrand(request.getBrand());
        existingProduct.setPrice(request.getPrice());
        existingProduct.setInventory(request.getInventory());
        existingProduct.setDescription(request.getDescription());

        Category category = categoryRepository.findByName(request.getCategory().getName());
        existingProduct.setCategory(category);
        return existingProduct;
    }

    @Override
    public void deleteProductById(UUID id) {
        productRepository.findById(id)
        .ifPresentOrElse(productRepository::delete, () -> {
            throw new ResourceNotFoundException("Product not found!");
        });
    }

    @Override
    public List<Product> getAllProducts() {
        return productRepository.findAll();
    }

    @Override
    public List<Product> getProductsByCategory(String category) {
        return productRepository.findByCategoryName(category);
    }

    @Override
    public List<Product> getProductsByBrand(String brand) {
        return productRepository.findByBrand(brand);
    }

    @Override
    public List<Product> getProductsByCategoryAndBrand(String category, String brand) {
        
        return productRepository.findByCategoryNameAndBrand(category, brand);
    }

    @Override
    public List<Product> getProductsByName(String name) {
        
        return productRepository.findByName(name);
    }

    @Override
    public List<Product> getProductsByBrandAndName(String brand, String name) {
        
        return productRepository.findByBrandAndName(brand, name);
    }

    @Override
    public Long countProductsByBrandAndName(String brand, String name) {
        
        return productRepository.countByBrandAndName(brand, name);
    }

    @Override
    public List<ProductDto> convertToProductDtoList(List<Product> products) {
        return products.stream()
            .map(this::convertToProductDto)
            .toList();
    }

    @Override
    public ProductDto convertToProductDto(Product product) {
        ProductDto productDto = modelMapper.map(product, ProductDto.class);
        List<Image> images = imageRepository.findByProductId(product.getId());
        List<ImageDto> imageDtos = images.stream()
            .map(image -> modelMapper.map(image, ImageDto.class))
            .toList();
        productDto.setImages(imageDtos);
        return productDto; 
    }
}
