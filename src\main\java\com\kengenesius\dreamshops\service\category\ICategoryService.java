package com.kengenesius.dreamshops.service.category;

import java.util.List;
import java.util.UUID;

import com.kengenesius.dreamshops.model.Category;

public interface ICategoryService {

    Category getCategoryById(UUID id);
    Category getCategoryByName(String name);

    Category addCategory(Category category);
    Category updateCategory(Category category, UUID id);
    void deleteCategoryById(UUID id);

    List<Category> getAllCategories();

}
