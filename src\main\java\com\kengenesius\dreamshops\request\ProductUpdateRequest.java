package com.kengenesius.dreamshops.request;

import java.math.BigDecimal;
import java.util.UUID;

import com.kengenesius.dreamshops.model.Category;

import lombok.Data;

@Data
public class ProductUpdateRequest {
    private UUID id;
    private String name;
    private String brand;
    private BigDecimal price;
    private int inventory;
    private String description;
    private Category category;
}
