package com.kengenesius.dreamshops.model;

import java.util.Collection;
import java.util.HashSet;
import java.util.UUID;

import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UuidGenerator;
import org.hibernate.type.SqlTypes;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToMany;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
public class ShopUserRole {
    @Id
    @UuidGenerator(style = UuidGenerator.Style.RANDOM)
    @Column(columnDefinition = "char(36)")
    @JdbcTypeCode(SqlTypes.CHAR)    
    private UUID id;
    private String name;

    public ShopUserRole(String name) {
        this.name = name;
    }

    @ManyToMany(mappedBy = "roles")
    private Collection<ShopUser> users = new HashSet<>();
}
