package com.kengenesius.dreamshops.service.image;

import java.util.List;
import java.util.UUID;

import org.springframework.web.multipart.MultipartFile;

import com.kengenesius.dreamshops.dto.ImageDto;
import com.kengenesius.dreamshops.model.Image;

public interface IImageService {
    Image getImageById(UUID id);
    void deleteImageById(UUID id);
    List<ImageDto> saveImages(List<MultipartFile> files, UUID productId);
    void updateImage(MultipartFile file, UUID imageId);
}
