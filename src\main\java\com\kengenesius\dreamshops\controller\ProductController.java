package com.kengenesius.dreamshops.controller;

import static org.springframework.http.HttpStatus.CONFLICT;
import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;
import static org.springframework.http.HttpStatus.NOT_FOUND;

import java.util.List;
import java.util.UUID;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.kengenesius.dreamshops.dto.ProductDto;
import com.kengenesius.dreamshops.exception.AlreadyExistsException;
import com.kengenesius.dreamshops.exception.ResourceNotFoundException;
import com.kengenesius.dreamshops.model.Product;
import com.kengenesius.dreamshops.request.ProductAddRequest;
import com.kengenesius.dreamshops.request.ProductUpdateRequest;
import com.kengenesius.dreamshops.response.ApiResponse;
import com.kengenesius.dreamshops.service.product.IProductService;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("${api.prefix}/products")
@RequiredArgsConstructor
public class ProductController {
    private final IProductService productService;

    @GetMapping("/all")
    public ResponseEntity<ApiResponse> getAllProducts()
    {
        List<Product> products = productService.getAllProducts();
        List<ProductDto> productDtos = productService.convertToProductDtoList(products);
        return ResponseEntity.ok(new ApiResponse("Found!", productDtos));
    }

    @GetMapping("/product/{id}")
    public ResponseEntity<ApiResponse> getProductById(@PathVariable UUID id)
    {
        try{
            Product product = productService.getProductById(id);
            ProductDto productDto = productService.convertToProductDto(product);
            return ResponseEntity.ok(new ApiResponse("Found!", productDto));
        }catch(ResourceNotFoundException e)
        {
            return ResponseEntity.status(NOT_FOUND).body(new ApiResponse(e.getMessage(), null));
        }
    }

    @GetMapping("/product/by/brand-and-name")
    public ResponseEntity<ApiResponse> getProductByBrandAndName(@RequestParam String brand, @RequestParam String name)
    {
        try{
            List<Product> products = productService.getProductsByBrandAndName(brand, name);
            if(products.isEmpty()){
                return ResponseEntity.status(NOT_FOUND).body(new ApiResponse("Not found!", null));
            }
            List<ProductDto> productDtos = productService.convertToProductDtoList(products);
            return ResponseEntity.ok(new ApiResponse("Found!", productDtos));
        }catch(Exception e)
        {
            return ResponseEntity.status(INTERNAL_SERVER_ERROR).body(new ApiResponse(e.getMessage(), null));
        }
    }

    @GetMapping("/product/by/category-and-brand")
    public ResponseEntity<ApiResponse> getProductByCategoryAndBrand(@RequestParam String category, @RequestParam String brand)
    {
        try{
            List<Product> products = productService.getProductsByCategoryAndBrand(category, brand);
            if(products.isEmpty()){
                return ResponseEntity.status(NOT_FOUND).body(new ApiResponse("Not found!", null));
            }
            List<ProductDto> productDtos = productService.convertToProductDtoList(products);
            return ResponseEntity.ok(new ApiResponse("Found!", productDtos));
        }catch(Exception e)
        {
            return ResponseEntity.status(INTERNAL_SERVER_ERROR).body(new ApiResponse(e.getMessage(), null));
        }
    }

    @GetMapping("/product/by/name")
    public ResponseEntity<ApiResponse> getProductByName(@RequestParam String name){
        try{
            List<Product> products = productService.getProductsByName(name);
            if(products.isEmpty()){
                return ResponseEntity.status(NOT_FOUND).body(new ApiResponse("Not found!", null));
            }
            List<ProductDto> productDtos = productService.convertToProductDtoList(products);
            return ResponseEntity.ok(new ApiResponse("Found!", productDtos));
        }catch(Exception e)
        {
            return ResponseEntity.status(INTERNAL_SERVER_ERROR).body(new ApiResponse(e.getMessage(), null));
        }
    }

    @GetMapping("/product/by/brand")
    public ResponseEntity<ApiResponse> getProductByBrand(@RequestParam String brand){
        try{
            List<Product> products = productService.getProductsByBrand(brand);
            if(products.isEmpty()){
                return ResponseEntity.status(NOT_FOUND).body(new ApiResponse("Not found!", null));
            }
            List<ProductDto> productDtos = productService.convertToProductDtoList(products);
            return ResponseEntity.ok(new ApiResponse("Found!", productDtos));
        }catch(Exception e)
        {
            return ResponseEntity.status(INTERNAL_SERVER_ERROR).body(new ApiResponse(e.getMessage(), null));
        }
    }

    @GetMapping("/product/by/category")
    public ResponseEntity<ApiResponse> getProductByCategory(@RequestParam String category){
        try{
            List<Product> products = productService.getProductsByCategory(category);
            if(products.isEmpty()){
                return ResponseEntity.status(NOT_FOUND).body(new ApiResponse("Not found!", null));
            }
            List<ProductDto> productDtos = productService.convertToProductDtoList(products);
            return ResponseEntity.ok(new ApiResponse("Found!", productDtos));
        }catch(Exception e)
        {
            return ResponseEntity.status(INTERNAL_SERVER_ERROR).body(new ApiResponse(e.getMessage(), null));
        }
    }

    @PostMapping("/add")
    public ResponseEntity<ApiResponse> addProduct(@RequestBody ProductAddRequest product)
    {
        try{
            Product addedProduct = productService.addProduct(product);
            ProductDto productDto = productService.convertToProductDto(addedProduct);
            return ResponseEntity.ok(new ApiResponse("Success", productDto));
        }catch(AlreadyExistsException e)
        {
            return ResponseEntity.status(CONFLICT).body(new ApiResponse(e.getMessage(), null));
        }
    }

    @PutMapping("/product/{id}/update")
    public ResponseEntity<ApiResponse> updateProduct(@PathVariable UUID id, @RequestBody ProductUpdateRequest product)
    {
        try{
            Product updatedProduct = productService.updateProduct(product, id);
            ProductDto productDto = productService.convertToProductDto(updatedProduct);
            return ResponseEntity.ok(new ApiResponse("Updated!", productDto));
        }catch(ResourceNotFoundException e)
        {
            return ResponseEntity.status(INTERNAL_SERVER_ERROR).body(new ApiResponse(e.getMessage(), null));
        }
    }

    @DeleteMapping("/product/{id}/delete")
    public ResponseEntity<ApiResponse> deleteProduct(@PathVariable UUID id)
    {
        try{
            productService.deleteProductById(id);
            return ResponseEntity.ok(new ApiResponse("Deleted!", null));
        }catch(ResourceNotFoundException e)
        {
            return ResponseEntity.status(NOT_FOUND).body(new ApiResponse(e.getMessage(), null));
        }
    }

    @GetMapping("/product/count/by/brand-and-name")
    public ResponseEntity<ApiResponse> countProductByBrandAndName(@RequestParam String brand, @RequestParam String name)
    {
        try{
            Long productCount = productService.countProductsByBrandAndName(brand, name);
            return ResponseEntity.ok(new ApiResponse("Found!", productCount));
        }catch(Exception e)
        {
            return ResponseEntity.status(INTERNAL_SERVER_ERROR).body(new ApiResponse(e.getMessage(), null));
        }
    }
}
