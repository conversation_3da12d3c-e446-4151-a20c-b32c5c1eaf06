package com.kengenesius.dreamshops.service.cartitem;

import java.util.UUID;

import com.kengenesius.dreamshops.model.CartItem;

public interface ICartItemService {
    void addItemToCart(UUID cartId, UUID productId, int quantity);
    void removeItemFromCart(UUID cartId, UUID productId);
    void updateItemQuantity(UUID cartId, UUID productId, int quantity);
    CartItem getCartItem(UUID cartId, UUID productId);
}
