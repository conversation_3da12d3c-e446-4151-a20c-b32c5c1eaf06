package com.kengenesius.dreamshops.service.shopuser;

import java.util.UUID;

import com.kengenesius.dreamshops.dto.ShopUserDto;
import com.kengenesius.dreamshops.model.ShopUser;
import com.kengenesius.dreamshops.request.CreateUserRequest;
import com.kengenesius.dreamshops.request.UpdateUserRequest;

public interface IShopUserService {
    ShopUser getUserById(UUID userId);
    ShopUser createUser(CreateUserRequest request);
    ShopUser updateUser(UpdateUserRequest request, UUID userId);
    void deleteUser(UUID userId);
    ShopUserDto convertToShopUserDto(ShopUser user);
}
